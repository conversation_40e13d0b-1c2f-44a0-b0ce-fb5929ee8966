// Main exports for the Firestore REST API library with LC Backend Integration

// Core classes
export { FirestoreClient } from "./client";
export { FirestoreOperations } from "./operations";
export { LCFirebaseAuth, TokenManager } from "./auth";

// Import types and classes for internal use
import type {
  LCAuthConfig,
  DocumentData,
  QueryOptions,
  WhereFilter,
} from "./types";
import { FirestoreClient } from "./client";
import { FirestoreOperations } from "./operations";

// Types
export type {
  LCAuthConfig,
  LCAuthToken,
  FirestoreDocument,
  FirestoreValue,
  DocumentData,
  QueryOptions,
  WhereFilter,
  OrderBy,
  UseFirestoreResult,
  UseFirestoreListResult,
} from "./types";

// Export LCIntegrationConfig and utilities from lcIntegration
export type { LCIntegrationConfig } from "./lcIntegration";
export { createLCFirestore, createLCAuthTokenGetter, useLCFirestore } from "./lcIntegration";

// Utilities
export {
  toFirestoreValue,
  fromFirestoreValue,
  toFirestoreFields,
  fromFirestoreFields,
  extractDocumentId,
  buildDocumentPath,
  validateCollectionName,
  validateDocumentId,
  generateDocumentId,
  handleFirestoreError,
} from "./utils";

// React hooks (SSR/CSR compatible)
export {
  useDocument,
  useCollection,
  useQuery,
  usePagination,
  useRealtimeDocument,
  useSearch,
  useFirestoreCRUD,
  useFirestoreAuth,
} from "./hooks";

// Note: For explicit client-side usage in Next.js, import from "./client-hooks" instead

// Validation schemas
export {
  firestoreConfigSchema,
  lcAuthConfigSchema,
  whereFilterSchema,
  queryOptionsSchema,
  isFirestoreError,
} from "./types";

/**
 * Automatically detect cookie header in Next.js server environments
 */
const getServerCookieHeader = (): string | undefined => {
  try {
    // Try to get from Next.js headers if available
    // Use dynamic import to avoid build warnings
    const nextHeaders = eval('require')('next/headers');
    const headersList = nextHeaders.headers();
    return headersList.get('cookie') || undefined;
  } catch {
    // Not in Next.js server environment or headers not available
    return undefined;
  }
};

/**
 * Factory function to create a configured Firestore instance
 * Automatically handles token detection and synchronization between server/client
 */
export const createFirestore = (config: {
  projectId: string;
  apiKey: string;
  customToken?: string;
  getAuthToken?: () => Promise<string | null>;
  onTokenRefresh?: (token: string) => void;
  cookieHeader?: string; // For server-side usage (optional, auto-detected in Next.js)
}) => {
  const authConfig: LCAuthConfig = {
    projectId: config.projectId,
    apiKey: config.apiKey,
    ...(config.customToken !== undefined && { customToken: config.customToken }),
    ...(config.getAuthToken !== undefined && { getAuthToken: config.getAuthToken }),
    ...(config.onTokenRefresh !== undefined && { onTokenRefresh: config.onTokenRefresh }),
  };

  // Auto-detect cookie header if not provided and we're on server
  const cookieHeader = config.cookieHeader || getServerCookieHeader();

  const client = new FirestoreClient(authConfig, cookieHeader);
  const operations = new FirestoreOperations(client);

  return {
    client,
    operations,
    auth: client.getAuth(),
    // Convenience methods
    collection: (name: string) => ({
      add: (data: DocumentData) => operations.add(name, data),
      set: (id: string, data: DocumentData) => operations.set(name, id, data),
      get: (id: string) => operations.get(name, id),
      update: (id: string, data: DocumentData) => operations.update(name, id, data),
      delete: (id: string) => operations.delete(name, id),
      getAll: () => operations.getAll(name),
      where: (field: string, op: WhereFilter["op"], value: unknown) =>
        operations.where(name, field, op, value),
      orderBy: (field: string, direction: "asc" | "desc" = "asc") => 
        operations.orderBy(name, field, direction),
      limit: (count: number) => operations.limit(name, count),
      query: (options: QueryOptions) => operations.query(name, options),
      search: (field: string, term: string) => operations.search(name, field, term),
      paginate: (pageSize: number, pageToken?: string) => 
        operations.paginate(name, pageSize, pageToken),
      count: (filters?: WhereFilter[]) => operations.count(name, filters),
      exists: (id: string) => operations.exists(name, id),
    }),
    // Authentication helpers
    setCustomToken: (token: string) => client.setCustomToken(token),
    isAuthenticated: () => client.isAuthenticated(),
    getCurrentUser: () => client.getCurrentUser(),
    signOut: () => client.signOut(),
  };
};
/**
 * Default export for convenience
 */
export default createFirestore;
