# Cookie-Based Token Sharing Guide

This document explains how the refactored `@gd/firestore-rest` package now uses cookie-based token sharing for seamless authentication between server and client environments.

## Key Changes

### 1. Simplified API
The `createFirestore` function now works identically in both environments:

```typescript
// Works in both server and client
const firestore = createFirestore({
  projectId: process.env.FIREBASE_PROJECT_ID!,
  apiKey: process.env.FIREBASE_API_KEY!,
});
```

### 2. Automatic Token Detection
- **Client**: Reads tokens from localStorage and cookies
- **Server**: Reads tokens from cookies automatically
- **Synchronization**: Tokens are automatically shared via cookies

### 3. Hidden Implementation Details
Users no longer need to manage:
- Environment detection
- Storage abstraction
- Manual token passing
- Cookie management

## Usage Examples

### Client-Side Authentication

```typescript
// pages/login.tsx
"use client";

import { createFirestore } from "@gd/firestore-rest";

const LoginPage = () => {
  const firestore = createFirestore({
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY!,
  });

  const handleLogin = async (customToken: string) => {
    // Set the custom token - automatically stored in cookies
    firestore.setCustomToken(customToken);
    
    // The token is now available for server-side rendering
    // No manual cookie management needed!
  };

  return (
    <div>
      <button onClick={() => handleLogin("your-custom-token")}>
        Login
      </button>
    </div>
  );
};
```

### Server-Side Data Fetching

```typescript
// app/dashboard/page.tsx (App Router)
import { createFirestore } from "@gd/firestore-rest";

export default async function DashboardPage() {
  const firestore = createFirestore({
    projectId: process.env.FIREBASE_PROJECT_ID!,
    apiKey: process.env.FIREBASE_API_KEY!,
    // Cookie header is automatically detected in Next.js
  });

  // Automatically uses token from cookies set by client
  const users = await firestore.collection("users").getAll();

  return (
    <div>
      <h1>Dashboard</h1>
      {users.map(user => (
        <div key={user.id}>{user.name}</div>
      ))}
    </div>
  );
}
```

### API Routes

```typescript
// app/api/users/route.ts
import { createFirestore } from "@gd/firestore-rest";

export async function GET() {
  const firestore = createFirestore({
    projectId: process.env.FIREBASE_PROJECT_ID!,
    apiKey: process.env.FIREBASE_API_KEY!,
  });

  // Automatically reads token from request cookies
  const users = await firestore.collection("users").getAll();
  
  return Response.json(users);
}
```

### React Hooks (Universal)

```tsx
// components/UserList.tsx
import { useCollection } from "@gd/firestore-rest";

const UserList = () => {
  const firestore = createFirestore({
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY!,
  });

  // Works in both SSR and CSR
  const { data: users, loading, error } = useCollection(
    firestore.operations,
    "users"
  );

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      {users?.map(user => (
        <div key={user.id}>{user.name}</div>
      ))}
    </div>
  );
};
```

## Token Flow

1. **User logs in on client** → Custom token received from LC backend
2. **Client stores token** → Automatically saved to localStorage + cookies
3. **Server-side rendering** → Automatically reads token from cookies
4. **Token refresh** → Automatically handled and synced via cookies
5. **Logout** → Clears tokens from all storage locations

## Advanced Usage

### Manual Cookie Header (Edge Cases)

```typescript
// For custom server environments
import { headers } from 'next/headers';

const cookieHeader = headers().get('cookie');
const firestore = createFirestore({
  projectId: process.env.FIREBASE_PROJECT_ID!,
  apiKey: process.env.FIREBASE_API_KEY!,
  cookieHeader, // Manual override
});
```

### Custom Token Management

```typescript
const firestore = createFirestore({
  projectId: process.env.FIREBASE_PROJECT_ID!,
  apiKey: process.env.FIREBASE_API_KEY!,
  getAuthToken: async () => {
    // Custom token retrieval logic
    return await getTokenFromCustomSource();
  },
});
```

## Migration from Previous Version

### Before (Complex)
```typescript
import { createFirestore, universalStorage, isBrowser } from "@gd/firestore-rest";

// Manual environment handling
const firestore = createFirestore({
  projectId: process.env.FIREBASE_PROJECT_ID!,
  apiKey: process.env.FIREBASE_API_KEY!,
  onTokenRefresh: (token) => {
    if (isBrowser()) {
      universalStorage.setItem('token', token);
    }
  },
});
```

### After (Simple)
```typescript
import { createFirestore } from "@gd/firestore-rest";

// Automatic environment handling
const firestore = createFirestore({
  projectId: process.env.FIREBASE_PROJECT_ID!,
  apiKey: process.env.FIREBASE_API_KEY!,
});
```

## Benefits

- **Zero Configuration**: Works out of the box in both environments
- **Automatic Synchronization**: Tokens shared seamlessly via cookies
- **Better Performance**: Server-side rendering with authenticated data
- **Simplified Code**: No manual environment or storage management
- **Type Safety**: Full TypeScript support maintained

The package now provides a truly universal authentication experience with minimal configuration required.
